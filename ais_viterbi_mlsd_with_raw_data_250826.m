clear all;

%-------------------------------------------------------------------------
% Raw data input
%-------------------------------------------------------------------------
fileID = fopen('./DumpData/DUMPDATA_250818_9_ch1.bin');   %Channel 1 raw data
%fileID = fopen('Success_Packets_20250715_104824\Success_Packet_001.bin');
%fileID = fopen('CRC_Error_Data_20250715_104824\CRC_Error_Packet_001.bin');
source_data = fread(fileID, 'uint16');

%-------------------------------------------------------------------------
% Defines
%-------------------------------------------------------------------------
OSR = 5;                                % Over sampling rate
BT  = 0.4;                              % Bandwidth time

PACKET_PERIOD       = 198;              % Packet period
PACKET_PERIOD_OS    = PACKET_PERIOD*OSR;
LEN_PSF             = 13;            % Pulse shaping filter length

INF                 = 1e5;              % 무한대 값 (초기화용)

% signal_gain, bias 계산 방법 선택 (테스트용)
USE_FAST_CORRELATION = 1;               % 0: 정규방정식 방법, 1: 단순 상관관계 방법
COMPARE_BOTH_METHODS = 0;               % 1: 두 방법 모두 테스트하여 비교, 0: 선택된 방법만 사용

MAX_SYNC_CORRVAL    = .780;
MAX_SYNC_COUNT      = 10;
TIMING_OFFSET       = -7;

data_start_index    = 0;
data_count          = 0;
mlsd_bit_count      = 0;
packet_bit_count    = 0;
packet_bit_data     = zeros(1, PACKET_PERIOD_OS);
direct_decision     = zeros(1, PACKET_PERIOD_OS);
bit_shift_reg       = 0;
bit_crc_reg         = 0xffff;
max_bit_size        = 72;


 G_vReverDataTableX     = [ ...
  %   0    1    2    3   4    5    6    7   8    9    a    b   c    d    e    f 
      0,  -1,  -1,  -1, 32,  -1,  -1,  -1, 16,  -1,  -1,  -1, 48,  -1,  -1,  -1, ... % 00--0f
      8,  -1,  -1,  -1, 40,  -1,  -1,  -1, 24,  -1,  -1,  -1, 56,  -1,  -1,  -1, ... % 10--1f
      4,  -1,  -1,  -1, 36,  -1,  -1,  -1, 20,  -1,  -1,  -1, 52,  -1,  -1,  -1, ... % 20--2f
     12,  -1,  -1,  -1, 44,  -1,  -1,  -1, 28,  -1,  -1,  -1, 60,  -1,  -1,  -1, ... % 30--3f
      2,  -1,  -1,  -1, 34,  -1,  -1,  -1, 18,  -1,  -1,  -1, 50,  -1,  -1,  -1, ... % 40--4f
     10,  -1,  -1,  -1, 42,  -1,  -1,  -1, 26,  -1,  -1,  -1, 58,  -1,  -1,  -1, ... % 50--5f
      6,  -1,  -1,  -1, 38,  -1,  -1,  -1, 22,  -1,  -1,  -1, 54,  -1,  -1,  -1, ... % 60--6f
     14,  -1,  -1,  -1, 46,  -1,  -1,  -1, 30,  -1,  -1,  -1, 62,  -1,  -1,  -1, ... % 70--7f
      1,  -1,  -1,  -1, 33,  -1,  -1,  -1, 17,  -1,  -1,  -1, 49,  -1,  -1,  -1, ... % 80--8f
      9,  -1,  -1,  -1, 41,  -1,  -1,  -1, 25,  -1,  -1,  -1, 57,  -1,  -1,  -1, ... % 90--9f
      5,  -1,  -1,  -1, 37,  -1,  -1,  -1, 21,  -1,  -1,  -1, 53,  -1,  -1,  -1, ... % a0--af
     13,  -1,  -1,  -1, 45,  -1,  -1,  -1, 29,  -1,  -1,  -1, 61,  -1,  -1,  -1, ... % b0--bf
      3,  -1,  -1,  -1, 35,  -1,  -1,  -1, 19,  -1,  -1,  -1, 51,  -1,  -1,  -1, ... % c0--cf
     11,  -1,  -1,  -1, 43,  -1,  -1,  -1, 27,  -1,  -1,  -1, 59,  -1,  -1,  -1, ... % d0--df
      7,  -1,  -1,  -1, 39,  -1,  -1,  -1, 23,  -1,  -1,  -1, 55,  -1,  -1,  -1, ... % e0--ef
     15,  -1,  -1,  -1, 47,  -1,  -1,  -1, 31,  -1,  -1,  -1, 63,  -1,  -1,  -1];    % f0--ff

 G_vMaxBitSize          = [ ...
     1064,  168,  168,  168,  168,  424, 1008,  168, 1008,  168,   72,  168, 1008,  168, 1008,  160, ...
      144,  816,  168,  312,  160,  360,  168,  160,  168,  168, 1064,   96, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, ...
     1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064, 1064];



%-------------------------------------------------------------------------
% Rx Operation Mode
%-------------------------------------------------------------------------
RX_MDM_STATUS_PREAMBLE  = 0;
RX_MDM_STATUS_PRELOAD   = 1;
RX_MDM_STATUS_DATA      = 2;

rx_run_status           = RX_MDM_STATUS_PREAMBLE;
max_correlation_value   = 0;
max_correlation_count   = 0;

count_adc_error         = 0;

preamble_detect_count   = 0;
id_error_count          = 0;
crc_error_count         = 0;
length_error_count      = 0;
packet_ok_count         = 0;
adc_error_count         = 0;

ADC_MAX_ERROR_CNT       = 20;
DC_MIN_LEVEL            = (  50 / 3300) * 4096;    % 0.05V
DC_MAX_LEVEL            = (1850 / 3300) * 4096;    % 1.85V
%-------------------------------------------------------------------------
% Traning sequence(24bit : 0101...) and start flag(0x7E)
%-------------------------------------------------------------------------
dot_pattern       = repmat([-1, -1, 1, 1], 1, 4);   % Dot pattern
start_pattern     = [-1, -1, -1, -1, -1, -1, -1, 1];% Start pattern
preamble          = [dot_pattern, start_pattern]';  % Preamble
preamble_os       = repelem(preamble, OSR);         % Over sampled preamble
LEN_DOT_PATTERN   = length(dot_pattern);            % Length of dot pattern
LEN_START_PATTERN = length(start_pattern);          % Length of start pattern
LEN_PREAMBLE      = length(preamble);               % Length of preamble
LEN_PREAMBLE_OS   = LEN_PREAMBLE*OSR;               % Length of over sampled preamble

%-------------------------------------------------------------------------
% impulse response of gmsk, gauss filter
%-------------------------------------------------------------------------
%[impulse_response_of_gmsk, kkk] = gmsk_impulse_response(BT, OSR, LEN_PSF, 2);

%impulse_response_of_gmsk = [ 0.0097, 0.0369, 0.1027, 0.2143,...
%                             0.3462, 0.4502, 0.4888, 0.4502,...
%                             0.3462, 0.2143, 0.1027, 0.0369,...
%                             0.0097];

impulse_response_of_gmsk = [0.0199, 0.0755, 0.2100, 0.4384,...
                            0.7082, 0.9209, 1     , 0.9209,...
                            0.7082, 0.4384, 0.2100, 0.0755,...
                            0.0199];

%impulse_response_of_gmsk = [199, 755, 2100, 4384, 7082, 9209, 10000, 9209, 7082, 4384, 2100, 755, 199];

%SPAN = 3; SPS = 4;
%impulse_response_of_gmsk        = gaussdesign(BT, SPAN, SPS);
preamble_zero_padded            = upsample(preamble, OSR);
preamble_filtered_by_gmsk       = conv(preamble_zero_padded, impulse_response_of_gmsk);
preamble_range                  = 1:length(preamble_filtered_by_gmsk);
half_impulse_response_os        = convolved_half_impulse_response_os(impulse_response_of_gmsk, 1);

%-------------------------------------------------------------------------
% Viterbi 상태 정보를 모두 포함하는 통합 구조체
%-------------------------------------------------------------------------
viterbi_states = struct();

% 4개 상태에 대한 정보를 배열로 관리
viterbi_states.sequences = cell(4, 1);     % 비트 시퀀스
viterbi_states.path_metrics = zeros(4, 1); % 경로 메트릭

% 상태 매핑 정보
viterbi_states.state_map = {'00', '01', '10', '11'};

%-------------------------------------------------------------------------
%{
figure(1);
subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse\_response (gmsk)');
subplot(3,1,2); plot(preamble, '-o'); grid; title('preamble');
subplot(3,1,3); plot(preamble_range, preamble_filtered_by_gmsk(preamble_range), '-o');
                    grid; title('preamble\_filtered (gmsk) (o), (gmsk twice) (x)');
%}
%-------------------------------------------------------------------------

%-------------------------------------------------------------------------
% functions
%-------------------------------------------------------------------------
% viterbi_mlsd
% Maximum Likelihood Sequence Detection (최대우도 시퀀스 검출) 함수

% Viterbi 알고리즘을 사용하여 ISI(Inter-Symbol Interference)가 있는 채널에서
% 최적의 비트 시퀀스를 복원하는 함수
%
% 입력 파라미터:
%   rx_data                       : 수신된 신호 데이터
%   main_signal_coeff             : 주 신호 계수 (현재 심볼 영향)
%   isi_signal_coeff              : ISI 신호 계수 (이전/이후 심볼 간섭 영향)
%   LOCAL_RANGE_ONE_PERIOD_BAUD_RATE : 처리할 샘플 범위
%
% 출력 파라미터:
%   data_seq_00                   : 복원된 비트 시퀀스 (상태 00에서 종료)
%
% 동작 원리:
% 1. 4개 상태 (00, 01, 10, 11)를 가진 트렐리스 구조 사용
% 2. 각 상태는 이전 2비트의 조합을 나타냄 (메모리 길이 = 2)
% 3. ISI를 고려한 8가지 예상 신호값을 미리 계산
% 4. Viterbi 알고리즘으로 최소 누적 메트릭 경로를 추적
function [viterbi_states] = viterbi_mlsd(viterbi_states, rx_data, main_signal_coeff, isi_signal_coeff)

    % ISI를 고려한 8가지 예상 신호 값 계산
    % 형태: [이전비트, 현재비트, 다음비트]의 조합에 따른 신호값
    % h_values(1): [-1,-1,-1] → -isi_coeff-main_coeff-isi_coeff
    % h_values(2): [-1,-1,+1] → -isi_coeff-main_coeff+isi_coeff
    % h_values(3): [-1,+1,-1] → -isi_coeff+main_coeff-isi_coeff
    % h_values(4): [-1,+1,+1] → -isi_coeff+main_coeff+isi_coeff
    % h_values(5): [+1,-1,-1] → +isi_coeff-main_coeff-isi_coeff
    % h_values(6): [+1,-1,+1] → +isi_coeff-main_coeff+isi_coeff
    % h_values(7): [+1,+1,-1] → +isi_coeff+main_coeff-isi_coeff
    % h_values(8): [+1,+1,+1] → +isi_coeff+main_coeff+isi_coeff
    h_values = [-isi_signal_coeff - main_signal_coeff - isi_signal_coeff, ...
                -isi_signal_coeff - main_signal_coeff + isi_signal_coeff, ...
                -isi_signal_coeff + main_signal_coeff - isi_signal_coeff, ...
                -isi_signal_coeff + main_signal_coeff + isi_signal_coeff, ...
                 isi_signal_coeff - main_signal_coeff - isi_signal_coeff, ...
                 isi_signal_coeff - main_signal_coeff + isi_signal_coeff, ...
                 isi_signal_coeff + main_signal_coeff - isi_signal_coeff, ...
                 isi_signal_coeff + main_signal_coeff + isi_signal_coeff];

    % ========== 상태 00 (이전 2비트: 00) 처리 ==========
    % 상태 00으로 들어올 수 있는 경로:
    % 1) 상태 00에서 비트 0 입력 (00 → 00)
    % 2) 상태 10에서 비트 0 입력 (10 → 00)

    % 브랜치 메트릭 계산 (수신신호와 예상신호의 거리 제곱)
    bm_00_0 = (rx_data - h_values(1))^2;  % 상태 00→00 (비트패턴: 000)
    bm_10_0 = (rx_data - h_values(5))^2;  % 상태 10→00 (비트패턴: 100)

    % 경로 메트릭 후보 계산
    pm_cand1 = viterbi_states.path_metrics(1) + bm_00_0;      % 상태 00에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics(3) + bm_10_0;      % 상태 10에서 오는 경로

    % 생존 경로 선택 (Add-Compare-Select)
    if (pm_cand1 < pm_cand2)
        pm_00 = pm_cand1;                       % 더 작은 메트릭 선택
        data_seq_00 = [viterbi_states.sequences{1}; 0];     % 비트 0(-1) 추가, 상태 00 경로 이어받기
    else
        pm_00 = pm_cand2;                       % 더 작은 메트릭 선택
        data_seq_00 = [viterbi_states.sequences{3}; 0];     % 비트 0(-1) 추가, 상태 10 경로 이어받기
    end

    % ========== 상태 01 (이전 2비트: 01) 처리 ==========
    % 상태 01로 들어올 수 있는 경로:
    % 1) 상태 00에서 비트 1 입력 (00 → 01)
    % 2) 상태 10에서 비트 1 입력 (10 → 01)

    bm_00_1 = (rx_data - h_values(2))^2;  % 상태 00→01 (비트패턴: 001)
    bm_10_1 = (rx_data - h_values(6))^2;  % 상태 10→01 (비트패턴: 101)

    pm_cand1 = viterbi_states.path_metrics(1) + bm_00_1;      % 상태 00에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics(3) + bm_10_1;      % 상태 10에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_01 = pm_cand1;
        data_seq_01 = [viterbi_states.sequences{1}; 1];     % 비트 1(+1) 추가, 상태 00 경로 이어받기
    else
        pm_01 = pm_cand2;
        data_seq_01 = [viterbi_states.sequences{3}; 1];     % 비트 1(+1) 추가, 상태 10 경로 이어받기
    end

    % ========== 상태 10 (이전 2비트: 10) 처리 ==========
    % 상태 10으로 들어올 수 있는 경로:
    % 1) 상태 01에서 비트 0 입력 (01 → 10)
    % 2) 상태 11에서 비트 0 입력 (11 → 10)

    bm_01_0 = (rx_data - h_values(3))^2;  % 상태 01→10 (비트패턴: 010)
    bm_11_0 = (rx_data - h_values(7))^2;  % 상태 11→10 (비트패턴: 110)

    pm_cand1 = viterbi_states.path_metrics(2) + bm_01_0;      % 상태 01에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics(4) + bm_11_0;      % 상태 11에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_10 = pm_cand1;
        data_seq_10 = [viterbi_states.sequences{2}; 0];    % 비트 0(-1) 추가, 상태 01 경로 이어받기
    else
        pm_10 = pm_cand2;
        data_seq_10 = [viterbi_states.sequences{4}; 0];    % 비트 0(-1) 추가, 상태 11 경로 이어받기
    end

    % ========== 상태 11 (이전 2비트: 11) 처리 ==========
    % 상태 11로 들어올 수 있는 경로:
    % 1) 상태 01에서 비트 1 입력 (01 → 11)
    % 2) 상태 11에서 비트 1 입력 (11 → 11)

    bm_01_1 = (rx_data - h_values(4))^2;  % 상태 01→11 (비트패턴: 011)
    bm_11_1 = (rx_data - h_values(8))^2;  % 상태 11→11 (비트패턴: 111)

    pm_cand1 = viterbi_states.path_metrics(2) + bm_01_1;      % 상태 01에서 오는 경로
    pm_cand2 = viterbi_states.path_metrics(4) + bm_11_1;      % 상태 11에서 오는 경로

    % 생존 경로 선택
    if (pm_cand1 < pm_cand2)
        pm_11 = pm_cand1;
        data_seq_11 = [viterbi_states.sequences{2}; 1];     % 비트 1(+1) 추가, 상태 01 경로 이어받기
    else
        pm_11 = pm_cand2;
        data_seq_11 = [viterbi_states.sequences{4}; 1];     % 비트 1(+1) 추가, 상태 11 경로 이어받기
    end

    % ========== 다음 시간 단계를 위한 상태 업데이트 ==========
    % 현재 시간 단계의 경로 메트릭과 데이터 시퀀스를 저장
    viterbi_states.path_metrics(1) = pm_00;                 % 상태 00의 경로 메트릭 저장
    viterbi_states.path_metrics(2) = pm_01;                 % 상태 01의 경로 메트릭 저장
    viterbi_states.path_metrics(3) = pm_10;                 % 상태 10의 경로 메트릭 저장
    viterbi_states.path_metrics(4) = pm_11;                 % 상태 11의 경로 메트릭 저장
    viterbi_states.sequences{1} = data_seq_00;              % 상태 00의 데이터 시퀀스 저장
    viterbi_states.sequences{2} = data_seq_01;              % 상태 01의 데이터 시퀀스 저장
    viterbi_states.sequences{3} = data_seq_10;              % 상태 10의 데이터 시퀀스 저장
    viterbi_states.sequences{4} = data_seq_11;              % 상태 11의 데이터 시퀀스 저장
end

%------------------------------------------------------------------------
% GMSK Filter 적용
function y = apply_gmsk_filter(x_buff, impulse_response)
    conv_data = conv(x_buff, impulse_response);
    y = conv_data(length(impulse_response));
end

% GMSK 임펄스 응답 생성 함수
%
% 입력 파라미터:
%   BT              - 대역폭-시간 곱 (Bandwidth-Time product)
%   OSR             - 오버샘플링 비율 (Over Sampling Rate)
%   LENGTH          - 임펄스 응답의 길이 (샘플 수)
%   H_NORMALIZATION - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력 파라미터:
%   h               - GMSK 임펄스 응답 벡터
%   k               - 시간 인덱스 벡터
%
% 기능:
%   GMSK(Gaussian Minimum Shift Keying) 변조에 사용되는 임펄스 응답을 생성
%   오차함수(erf)를 이용하여 GMSK 필터의 임펄스 응답을 계산
%   AIS 신호의 GMSK 변조 특성을 모델링하기 위해 사용
function [h, k] = gmsk_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
    % 임펄스 응답 벡터 초기화
    h = zeros(LENGTH, 1);

    % 시간 인덱스 생성 (중심을 0으로 하는 대칭 구조)
    k = (-LENGTH/2+1):(LENGTH/2);

    % 심볼 주기 설정
    Ts = 1;

    % GMSK 임펄스 응답 계산 (오차함수 기반)
    % 공식: h(t) = 0.5 * [erf(α(t+0.5T)) - erf(α(t-0.5T))]
    % 여기서 α = π*BT*sqrt(2/ln(2))
    h = 0.5*(erf(pi*BT*sqrt(2/log(2))*(k/OSR + 0.5)) - erf(pi*BT*sqrt(2/log(2))*(k/OSR - 0.5)));

    % 정규화 처리
    if H_NORMALIZATION == 1
        h = h/max(h);           % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h = h/norm(h);          % 유클리드 노름으로 정규화
    end
end


% Gaussian 임펄스 응답 생성 함수
%
% 입력 파라미터:
%   BT              - 대역폭-시간 곱 (Bandwidth-Time product)
%   OSR             - 오버샘플링 비율 (Over Sampling Rate)
%   LENGTH          - 임펄스 응답의 길이 (샘플 수)
%   H_NORMALIZATION - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력 파라미터:
%   h               - Gaussian 임펄스 응답 벡터
%   k               - 시간 인덱스 벡터
%
% 기능:
%   Gaussian 필터의 임펄스 응답을 생성
%   가우시안 함수를 이용하여 필터 특성을 모델링
%   GMSK 변조의 기본이 되는 Gaussian 펄스 성형 필터 구현
function [h, k] = gauss_impulse_response(BT, OSR, LENGTH, H_NORMALIZATION)
    % 임펄스 응답 벡터 초기화
    h = zeros(LENGTH, 1);

    % 시간 인덱스 생성 (중심을 0으로 하는 대칭 구조)
    k = (-LENGTH/2+1):(LENGTH/2);

    % 심볼 주기 설정
    Ts = 1;
    T = OSR;  % 오버샘플링을 고려한 시간 스케일

    % Gaussian 임펄스 응답 계산 (Svedek 공식 기반)
    % 공식: h(t) = sqrt(2π/ln(2)) * exp(-2/ln(2) * (BT*π*t)^2)
    h = 1*sqrt(2*pi/log(2))*exp(-2/log(2)*(BT*pi*(k/T)).^2);

    % 정규화 처리
    if H_NORMALIZATION == 1
        h = h/max(h);           % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h = h/norm(h);          % 유클리드 노름으로 정규화
    end
end

% estimate_signal_gain: 신호 게인과 DC 바이어스를 추정하는 함수
%
% 입력 파라미터:
%   received_preamble_os    : 수신된 프리앰블 신호 (오버샘플링됨)
%   source_preamble_os      : 송신 프리앰블 신호 (오버샘플링됨, 기준 신호)
%   half_impulse_response_os: 반쪽 임펄스 응답 (채널 특성)
%   FILTERED_TWICE          : 이중 필터링 여부 플래그
%
% 출력 파라미터:
%   signal_gain  : 신호 게인 (수신 신호의 크기 스케일링 팩터)
%   bias         : DC 바이어스 (신호의 직류 성분)
%
% 동작 원리:
% 1. 최소자승법(Least Squares)을 사용하여 수신 신호를 모델링
% 2. 수신신호 = signal_gain * 송신신호 + bias + 노이즈 형태로 가정
% 3. 정규방정식을 직접 풀어서 신호 게인과 바이어스를 고속으로 추정 (pinv 대신 직접 계산)
function [signal_gain, bias] = estimate_signal_gain(received_preamble_os, source_preamble_os, max_impulse_response_os)

    INDEX_START = 4;  % 단일 필터링: 14번째 샘플부터 시작

    % 신호 정렬 및 기본 변수 설정
    source_aligned = source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1);
    n = length(received_preamble_os);

    % 정규방정식의 계수들 직접 계산 (pinv 대신 고속 계산)
    % 최소자승법: A'*A*x = A'*b 형태에서
    % A = [source_aligned, ones(n,1)]이므로
    % A'*A = [sum(s^2), sum(s); sum(s), n]
    % A'*b = [sum(s*r); sum(r)]
    sum_s2 = sum(source_aligned .* source_aligned);    % sum(s^2)
    sum_s = sum(source_aligned);                       % sum(s)
    sum_sr = sum(source_aligned .* received_preamble_os); % sum(s*r)
    sum_r = sum(received_preamble_os);                 % sum(r)

    % 2x2 행렬의 역행렬을 직접 계산 (det = sum_s2*n - sum_s^2)
    det_val = sum_s2 * n - sum_s * sum_s;

    if abs(det_val) > 1e-10  % 특이행렬 방지 (수치적 안정성 확보)
        % 크래머 공식을 이용한 직접 해법
        gain_scale = (n * sum_sr - sum_s * sum_r) / det_val;
        bias = (sum_s2 * sum_r - sum_s * sum_sr) / det_val;
        signal_gain = gain_scale * max_impulse_response_os;
    else
        % 폴백: 특이행렬인 경우 단순 추정 방법 사용
        bias = mean(received_preamble_os) - mean(source_aligned);
        received_dc_removed = received_preamble_os - bias;
        gain_scale = sum(received_dc_removed .* source_aligned) / sum_s2;
        signal_gain = gain_scale * max_impulse_response_os;
    end

    %-------------------------------------------------------------------------
    %{
    figure(2);
    kkk = 1:length(received_preamble_os);  % x축 인덱스
    scale = signal_gain/max_impulse_response_os;  % 정규화된 스케일 팩터

    % 서브플롯 1: 수신된 프리앰블 신호
    subplot(3,1,1); plot(received_preamble_os); grid; title('received preamble signal');
    % 서브플롯 2: 정렬된 송신 프리앰블 신호
    subplot(3,1,2); plot(source_aligned); grid; title('source preamble signal (aligned)');
    % 서브플롯 3: 신호 비교 (스케일된 송신신호, 바이어스 추가된 신호, 수신신호)
    subplot(3,1,3); plot(kkk, scale*source_aligned, '-o', ...
                         kkk, scale*source_aligned-bias, '-x', ...
                         kkk, received_preamble_os, '-+');
                    grid; title('scaled source preamble(o), scaled source preamble with bias(x), received preamble signal(+)');
    %}
    %-------------------------------------------------------------------------

    signal_gain = abs(signal_gain);
    %bias = abs(bias);
end

% estimate_signal_gain_fast: 단순 상관관계 기반 신호 게인 추정 함수 (가장 빠른 방법)
%
% 입력 파라미터:
%   received_preamble_os    : 수신된 프리앰블 신호 (오버샘플링됨)
%   source_preamble_os      : 송신 프리앰블 신호 (오버샘플링됨, 기준 신호)
%   max_impulse_response_os : 임펄스 응답의 최대값
%   FILTERED_TWICE          : 이중 필터링 여부 플래그
%
% 출력 파라미터:
%   signal_gain  : 신호 게인 (수신 신호의 크기 스케일링 팩터)
%   bias         : DC 바이어스 (신호의 직류 성분)
%
% 특징:
%   - 계산량이 가장 적음 (O(n) 복잡도)
%   - 단순 평균과 상관관계만 사용
%   - 정확도는 약간 감소할 수 있지만 속도가 매우 빠름
function [signal_gain, bias] = estimate_signal_gain_fast(received_preamble_os, source_preamble_os, max_impulse_response_os)

    INDEX_START = 4;   % 단일 필터링: 4번째 샘플부터 시작

    % 신호 정렬
    source_aligned = source_preamble_os(INDEX_START:INDEX_START+length(received_preamble_os)-1);

    % DC 바이어스 추정 (단순 평균 차이)
    bias = mean(received_preamble_os) - mean(source_aligned);

    % DC 제거된 수신 신호
    received_dc_removed = received_preamble_os - bias;

    % 채널 게인 추정 (상관관계 기반 - 정규화된 내적)
    % gain_scale = (received · source) / (source · source)
    numerator = sum(received_dc_removed .* source_aligned);
    denominator = sum(source_aligned .* source_aligned);

    if abs(denominator) > 1e-10  % 0으로 나누기 방지
        gain_scale = numerator / denominator;
    else
        gain_scale = 1;  % 폴백 값
    end

    % 실제 신호 게인 계산
    signal_gain = gain_scale * max_impulse_response_os;

    %-------------------------------------------------------------------------
    %{
    figure(2);
    kkk = 1:length(received_preamble_os);  % x축 인덱스
    scale = signal_gain/max_impulse_response_os;  % 정규화된 스케일 팩터

    % 서브플롯 1: 수신된 프리앰블 신호
    subplot(3,1,1); plot(received_preamble_os); grid; title('received preamble signal');
    % 서브플롯 2: 정렬된 송신 프리앰블 신호
    subplot(3,1,2); plot(source_aligned); grid; title('source preamble signal (aligned)');
    % 서브플롯 3: 신호 비교 (스케일된 송신신호, 바이어스 추가된 신호, 수신신호)
    subplot(3,1,3); plot(kkk, scale*source_aligned, '-o', ...
                         kkk, scale*source_aligned-bias, '-x', ...
                         kkk, received_preamble_os, '-+');
                    grid; title('scaled source preamble(o), scaled source preamble with bias(x), received preamble signal(+)');
    %}
    %-------------------------------------------------------------------------

    % 절댓값 처리 (원본 함수와 동일)
    signal_gain = abs(signal_gain);
    %bias = abs(bias);
end

% 컨볼루션된 반 임펄스 응답 생성 함수 (오버샘플링 적용)
%
% 입력 파라미터:
%   H_NORMALIZATION  - 정규화 방법 (1: 최대값 정규화, 2: 노름 정규화)
%
% 출력:
%   half_impulse_response_os - 컨볼루션된 반 임펄스 응답 (오버샘플링 적용)
%
% 기능:
%   두 개의 임펄스 응답을 생성하고 컨볼루션하여 최종 반 임펄스 응답을 생성
%   Viterbi MLSD 알고리즘에서 사용되는 채널 모델링을 위한 함수
function half_impulse_response_os = convolved_half_impulse_response_os(impulse_response_of_gmsk, H_NORMALIZATION)
    % 두 임펄스 응답의 컨볼루션 수행
    h_conv = conv (impulse_response_of_gmsk, impulse_response_of_gmsk);

    % 컨볼루션 결과에서 최대값의 인덱스 찾기 (피크 위치)
    index_max = find (h_conv == max(h_conv));

    % 정규화 방법에 따른 처리
    if H_NORMALIZATION == 1
        h_conv = h_conv/max(h_conv);        % 최대값으로 정규화
    elseif H_NORMALIZATION == 2
        h_conv = h_conv/norm(h_conv);       % 유클리드 노름으로 정규화
    end

    % 최대값 위치부터 length 길이만큼 추출하여 반 임펄스 응답 생성
    half_impulse_response_os = h_conv(index_max : length(h_conv)-1);

    % 디버깅 및 시각화를 위한 플롯 생성
    %{
    figure(3);
    subplot(3,1,1); plot(impulse_response_of_gmsk, '-o'); grid; title('impulse response of gmsk');
    subplot(3,1,2); plot(h_conv, '-o'); grid; title('h\_conv');
    subplot(3,1,3); plot(half_impulse_response_os, '-o'); grid; title('half impulse response of gmsk');
    %}
end

% CRC 계산 (AIS 표준)
function crc = update_crc(crc, new_bit)
    if bitand(bitxor(crc, new_bit), 0x0001)
        crc = bitxor(bitshift(crc, -1), 0x8408);
    else
        crc = bitshift(crc, -1);
    end
end


%-------------------------------------------------------------------------
% Main function
%-------------------------------------------------------------------------
rawdata_len             = length(source_data);
src_dc_level            = zeros(rawdata_len, 1);
dc_level                = zeros(rawdata_len, 1);
source_data_removed_dc  = zeros(rawdata_len, 1);
downsampled_data        = zeros(PACKET_PERIOD_OS, 1);

src_dc_level_sum        = 0;
dc_level_sum            = 0;
correlation_value       = zeros(rawdata_len, 1);
rawdata_excluded_dc     = zeros(LEN_PREAMBLE*OSR, 1);
received_preamble_os    = zeros(LEN_PREAMBLE_OS,1);
gmsk_filter_buffer      = zeros(1, length(impulse_response_of_gmsk));
rawdata_filtered        = zeros(rawdata_len, 1);

preamble_os2 = preamble_filtered_by_gmsk(4:4+LEN_PREAMBLE_OS-1);

for symbol_index = 1:rawdata_len
    gmsk_filter_buffer(1:1) = [];
    gmsk_filter_buffer(length(impulse_response_of_gmsk)) = source_data(symbol_index);

    % GMSK Filter
    rawdata_filtered(symbol_index) = apply_gmsk_filter(gmsk_filter_buffer, impulse_response_of_gmsk);

    % dc levels (moving average)
    if symbol_index <= LEN_DOT_PATTERN*OSR
        src_dc_level_sum = src_dc_level_sum + source_data(symbol_index);
        dc_level_sum = dc_level_sum + rawdata_filtered(symbol_index);
    else
        src_dc_level_sum = src_dc_level_sum + source_data(symbol_index) - source_data(symbol_index - LEN_DOT_PATTERN*OSR);
        dc_level_sum = dc_level_sum + rawdata_filtered(symbol_index) - rawdata_filtered(symbol_index - LEN_DOT_PATTERN*OSR);
    end
    src_dc_level(symbol_index) = src_dc_level_sum / (LEN_DOT_PATTERN*OSR);
    dc_level(symbol_index) = dc_level_sum / (LEN_DOT_PATTERN*OSR);

    if (rx_run_status == RX_MDM_STATUS_PREAMBLE)
        % correlations
        if symbol_index <= LEN_PREAMBLE_OS
            correlation_value(symbol_index) = 0;
        else
            rawdata_excluded_dc = rawdata_filtered(symbol_index - LEN_PREAMBLE_OS + 1 : symbol_index) - dc_level(symbol_index - LEN_PREAMBLE_OS + 1 : symbol_index);
            rawdata_excluded_dc = rawdata_excluded_dc/norm(rawdata_excluded_dc);
            correlation_value(symbol_index) = abs((rawdata_excluded_dc)' * preamble_os / norm(preamble_os));
            
        end

        if (correlation_value(symbol_index) > MAX_SYNC_CORRVAL && correlation_value(symbol_index) > max_correlation_value)
            max_correlation_value = correlation_value(symbol_index);
            max_correlation_count = 0;
            peak_correlation_index = symbol_index;
            data_start_index = peak_correlation_index + TIMING_OFFSET;
        elseif (max_correlation_value > MAX_SYNC_CORRVAL)
            max_correlation_count = max_correlation_count + 1;

            if (max_correlation_count >= MAX_SYNC_COUNT)
                rx_run_status = RX_MDM_STATUS_PRELOAD;
                preamble_detect_count = preamble_detect_count + 1;
                count_adc_error = 0;

                %fprintf('Sync index : %d\n', peak_correlation_index);

                % dc_level_fixed
                DC_LEVEL_FIXED = dc_level(peak_correlation_index - (LEN_START_PATTERN)*OSR);

                for idx = 1:LEN_PREAMBLE_OS
                    received_preamble_os(idx) = rawdata_filtered(peak_correlation_index - LEN_PREAMBLE_OS + (idx - 1)) - DC_LEVEL_FIXED;
                end
%{
                h_fig1 = figure(4);
                h_fig1.Name = 'Detected Preamble Data(Matched Filter)';
                if (symbol_index > 500)
                    index = symbol_index - 500;
                else
                    index = 1;
                end
                x1 = index:symbol_index+200;
                x2 = index:symbol_index;
                plot(x1, source_data(x1)*6, '-x', x2, rawdata_filtered(x2), '-o', x2, correlation_value(x2)*10000, '-+'); grid; 
                title('Detected\_Preamble_Data'); yline(DC_LEVEL_FIXED,'-m',DC_LEVEL_FIXED,'LineWidth',2);
                xline(peak_correlation_index,'--', 'Max picked correlation');
                %xline(peak_correlation_index-DC_AVG_COUNT-DC_AVG_OFFSET+1,'--', 'DC Offset sum start');
                %xline(peak_correlation_index-DC_AVG_OFFSET,'--', 'DC Offset sum end');
%}
                % signal_gain and dc_bias (방법 선택 가능)
                if COMPARE_BOTH_METHODS
                    % 두 방법 모두 테스트하여 성능 비교
                    fprintf('\n=== signal_gain, bias 계산 방법 비교 ===\n');

                    % 방법 1: 정규방정식 직접 해법
                    tic;
                    [signal_gain_normal, dc_bias_normal] = estimate_signal_gain(received_preamble_os, preamble_filtered_by_gmsk, max(impulse_response_of_gmsk));
                    time_normal = toc;

                    % 방법 2: 단순 상관관계 기반 방법
                    tic;
                    [signal_gain_fast, dc_bias_fast] = estimate_signal_gain_fast(received_preamble_os, preamble_filtered_by_gmsk, max(impulse_response_of_gmsk));
                    time_fast = toc;

                    % 결과 출력
                    fprintf('정규방정식 방법: signal_gain=%.4f, bias=%.4f, time=%.6f sec\n', signal_gain_normal, dc_bias_normal, time_normal);
                    fprintf('상관관계 방법:   signal_gain=%.4f, bias=%.4f, time=%.6f sec\n', signal_gain_fast, dc_bias_fast, time_fast);
                    fprintf('속도 향상:      %.1fx 빠름\n', time_normal/time_fast);
                    fprintf('signal_gain 차이: %.6f (%.2f%%)\n', abs(signal_gain_normal-signal_gain_fast), abs(signal_gain_normal-signal_gain_fast)/signal_gain_normal*100);
                    fprintf('bias 차이:      %.6f (%.2f%%)\n', abs(dc_bias_normal-dc_bias_fast), abs(dc_bias_normal-dc_bias_fast)/dc_bias_normal*100);

                    % 선택된 방법의 결과 사용
                    if USE_FAST_CORRELATION
                        signal_gain = signal_gain_fast;
                        dc_bias = dc_bias_fast;
                        fprintf('-> 상관관계 방법 결과 사용\n\n');
                    else
                        signal_gain = signal_gain_normal;
                        dc_bias = dc_bias_normal;
                        fprintf('-> 정규방정식 방법 결과 사용\n\n');
                    end
                else
                    % 선택된 방법만 사용
                    if USE_FAST_CORRELATION
                        [signal_gain, dc_bias] = estimate_signal_gain_fast(received_preamble_os, preamble_filtered_by_gmsk, max(impulse_response_of_gmsk));
                    else
                        [signal_gain, dc_bias] = estimate_signal_gain(received_preamble_os, preamble_filtered_by_gmsk, max(impulse_response_of_gmsk));
                    end
                end

                % MLSD용 신호 계수들 (half_impulse_response_os 기반)
                main_signal_coeff = half_impulse_response_os(1) * signal_gain;
                isi_signal_coeff = half_impulse_response_os(OSR+1) * signal_gain;

                % viterbi_states 초기화
                for i = 1:4
                    viterbi_states.sequences{i} = [];       % 빈 시퀀스로 초기화

                    % 경로 메트릭 초기화
                    % AIS Start bit 패턴이 "01"로 끝나므로 상태 01만 0으로 초기화
                    if (i == 2)
                        viterbi_states.path_metrics(i) = 0;
                    else
                        viterbi_states.path_metrics(i) = INF;
                    end
                end
            end
        else
            max_correlation_value = 0;
            max_correlation_count = 0;
            peak_correlation_index = 0;
            data_start_index = 0;
            data_count = 0;
            mlsd_bit_count = 0;
            bit_shift_reg = 0;
            bit_crc_reg = 0xffff;
            packet_bit_count = 0;
            continue;
        end
    else
        % Check Max/Min Lever voltage range
        if ((source_data(symbol_index) < DC_MIN_LEVEL || source_data(symbol_index) > DC_MAX_LEVEL))
            count_adc_error = count_adc_error + 1;
            if(count_adc_error > ADC_MAX_ERROR_CNT)
                rx_run_status = RX_MDM_STATUS_PREAMBLE;
                max_correlation_value = 0;
                max_correlation_count = 0;
                peak_correlation_index = 0;
                data_start_index = 0;
                data_count = 0;
                mlsd_bit_count = 0;
                bit_shift_reg = 0;
                bit_crc_reg = 0xffff;
                packet_bit_count = 0;
                count_adc_error = 0;

                adc_error_count = adc_error_count + 1;
                continue;
            end
        end

        if (data_start_index <= symbol_index)
            data_count = data_count + 1;
            downsampled_data(data_count) = rawdata_filtered(data_start_index) - DC_LEVEL_FIXED;

            % Direct decision
            if (data_count > 1)
                if ((rawdata_filtered(data_start_index) - DC_LEVEL_FIXED) > 0)
                    direct_decision(data_count-1) = 1;
                else
                    direct_decision(data_count-1) = 0;
                end
            end

            % Viterbi MLSD
            viterbi_states = viterbi_mlsd(viterbi_states, downsampled_data(data_count), main_signal_coeff, isi_signal_coeff);

            data_start_index = data_start_index + OSR;

            % MLSD
            if (   viterbi_states.sequences{1}(1) == viterbi_states.sequences{2}(1)...
                && viterbi_states.sequences{1}(1) == viterbi_states.sequences{3}(1)...
                && viterbi_states.sequences{1}(1) == viterbi_states.sequences{4}(1))
                
                mlsd_bit_count = mlsd_bit_count + 1;
                mlsd_detected_data(mlsd_bit_count) = viterbi_states.sequences{1}(1);

                if (mlsd_bit_count > 1)
                    bit_shift_reg = bitshift(bit_shift_reg, 1);

                    if (mlsd_detected_data(mlsd_bit_count) == mlsd_detected_data(mlsd_bit_count-1))
                        bit_shift_reg = bitor(bit_shift_reg, 0x0001);
                    else
                        bit_shift_reg = bitand(bit_shift_reg, 0xfffe);
                    end

                    switch(rx_run_status)
                        case RX_MDM_STATUS_PRELOAD
                            if (mlsd_bit_count == 9)
                                msg_id = G_vReverDataTableX(bitand(bitshift(bit_shift_reg, 2), 0x00ff) + 1);
                                if (msg_id < 0 || msg_id > 27)
                                    rx_run_status = RX_MDM_STATUS_PREAMBLE;
                                    max_correlation_value = 0;
                                    max_correlation_count = 0;
                                    peak_correlation_index = 0;
                                    data_start_index = 0;
                                    data_count = 0;
                                    mlsd_bit_count = 0;
                                    bit_shift_reg = 0;
                                    bit_crc_reg = 0xffff;
                                    packet_bit_count = 0;
                                    id_error_count = id_error_count + 1;
                                    continue;
                                else
                                    max_bit_size = (G_vMaxBitSize(msg_id + 1) + 16 + 12);
                                    rx_run_status = RX_MDM_STATUS_DATA;
                                end
                            end
                        case RX_MDM_STATUS_DATA
                            if (bitand(bit_shift_reg, 0x3f00) ~= 0x3e00)
                                new_bit = bitand(bitshift(bit_shift_reg, -8), 0x0001);
                                packet_bit_count = packet_bit_count + 1;

                                if (packet_bit_count > max_bit_size)
                                    rx_run_status = RX_MDM_STATUS_PREAMBLE;
                                    max_correlation_value = 0;
                                    max_correlation_count = 0;
                                    peak_correlation_index = 0;
                                    data_start_index = 0;
                                    data_count = 0;
                                    mlsd_bit_count = 0;
                                    bit_shift_reg = 0;
                                    bit_crc_reg = 0xffff;
                                    packet_bit_count = 0;

                                    length_error_count = length_error_count + 1;
                                    continue;
                                end

                                packet_bit_data(packet_bit_count) = new_bit;
                                bit_crc_reg = update_crc(bit_crc_reg, new_bit);
                            end

                            if (bitand(bit_shift_reg, 0x00ff) == 0x007e)
                                if(bit_crc_reg == 0xf0b8)
                                    fprintf('CRC OK at %d\n', peak_correlation_index);
                                    packet_ok_count = packet_ok_count + 1;
                                else
                                    fprintf('CRC NG at %d\n', peak_correlation_index);
                                    crc_error_count = crc_error_count + 1;
                                end

                                %{
                                if (packet_bit_count <= max_bit_size)
                                    %-------------------------------------------------------------------------
                                    figure(201);
                                    range1 = peak_correlation_index-LEN_PREAMBLE_OS*2:peak_correlation_index+PACKET_PERIOD_OS+30;
                                    range2 = 1:data_count;
                                    range3 = 1:mlsd_bit_count;
                                    range4 = 1:packet_bit_count;
                                    subplot(4,1,1); plot(range1, rawdata_filtered(range1), '-x', range1, dc_level(range1), '--', range1, correlation_value(range1)*1000, 'r-'); title('correlation'); grid;
                                    subplot(4,1,2); plot(range2, downsampled_data(range2), '-x'); title('rx data'); grid;
                                    subplot(4,1,3); plot(range3, mlsd_detected_data(range3), '-x', range3, direct_decision(range3), '-o'); title('estimated sequence(x), direct sequence(o)'); grid;
                                    subplot(4,1,4); plot(range4, packet_bit_data(range4), '-o'); title('real bit data(o)'); grid;
                                    %-------------------------------------------------------------------------
                                end
                                %}

                                rx_run_status = RX_MDM_STATUS_PREAMBLE;
                                max_correlation_value = 0;
                                max_correlation_count = 0;
                                peak_correlation_index = 0;
                                data_start_index = 0;
                                data_count = 0;
                                mlsd_bit_count = 0;
                                bit_shift_reg = 0;
                                bit_crc_reg = 0xffff;
                                packet_bit_count = 0;
                            end

                        otherwise
                            warning('Unexpected run status.');
                    end
                end

                viterbi_states.sequences{1}(1) = [];
                viterbi_states.sequences{2}(1) = [];
                viterbi_states.sequences{3}(1) = [];
                viterbi_states.sequences{4}(1) = [];
            end
        end
    end
end

%-------------------------------------------------------------------------
figure(9);
bar_x = ["preamble" "adc error" "id error" "length error" "crc error" "packet OK" ];
bar_y = [preamble_detect_count, adc_error_count, id_error_count, length_error_count, crc_error_count, packet_ok_count];
b = bar(bar_x, bar_y, 'FaceColor', 'flat');
b.CData(6,:) = [0.6350 0.0780 0.1840]; % red
xtips1 = b(1).XEndPoints;
ytips1 = b(1).YEndPoints;
labels1 = string(b(1).YData);
text(xtips1,ytips1,labels1,'HorizontalAlignment','center','VerticalAlignment','bottom')
%-------------------------------------------------------------------------