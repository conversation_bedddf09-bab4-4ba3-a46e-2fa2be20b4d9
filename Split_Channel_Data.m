fileID = fopen('./DumpData/AisDumpData_250818_11.txt');
source_data = fread(fileID, [inf], 'uint16'); 
fclose(fileID);

%rx_ch1_data;
%rx_ch2_data;

rx_ch1_cnt = 1;
rx_ch2_cnt = 1;

for i = 1:length(source_data)
    if mod(i,2) == 1
        rx_ch1_data(rx_ch1_cnt) = source_data(i);
        rx_ch1_cnt = rx_ch1_cnt + 1;
    else
        rx_ch2_data(rx_ch2_cnt) = source_data(i);
        rx_ch2_cnt = rx_ch2_cnt + 1;
    end
end

fileRx1 = fopen('./DumpData/DUMPDATA_250818_11_ch1.bin','w');
fwrite(fileRx1, rx_ch1_data, 'uint16');
fclose(fileRx1);

fileRx2 = fopen('./DumpData/DUMPDATA_250818_11_ch2.bin','w');
fwrite(fileRx2, rx_ch2_data, 'uint16');
fclose(fileRx2);