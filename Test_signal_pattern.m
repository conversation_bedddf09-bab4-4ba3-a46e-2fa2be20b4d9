clear all;

% 기본 파라미터
bitrate = 9600;          % AIS 비트율
deviation = 1740;        % 목표 편이

OSR = 5;                 % Over sampling rate
BT  = 0.4;               % Bandwidth time

% BT 0.4
%impulse_response_of_gmsk = [1, 24, 261, 1942, 10015, 35871, 89230, 154161, 184981, 154161, 89230, 35871, 10015, 1942, 261, 24, 1];
impulse_response_of_gmsk = [0.0199, 0.0755, 0.2100, 0.4384,...
                            0.7082, 0.9209, 1     , 0.9209,...
                            0.7082, 0.4384, 0.2100, 0.0755,...
                            0.0199];

dot_pattern = repmat([-1, 1], 1, 4);        % Dot pattern
preamble_os = upsample(dot_pattern, OSR);   % Over sampled preamble
preamble_filtered = conv(preamble_os, impulse_response_of_gmsk);


figure(1);
plot(preamble_filtered, '-o'); grid; title('impulse response of gmsk');